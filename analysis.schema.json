{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "title": "Maintenance Transaction Schema", "description": "Schema for equipment maintenance transaction records", "required": ["TransactionID", "EquipmentNumber", "EquipmentName", "SiteID", "MaintenanceType", "MaintenanceHours", "TransactionDate"], "properties": {"TransactionID": {"type": "integer", "description": "Unique identifier for the maintenance transaction", "minimum": 1}, "EquipmentNumber": {"type": "integer", "description": "Unique identifier for the equipment", "minimum": 1}, "EquipmentName": {"type": "string", "description": "Name or description of the equipment", "minLength": 1}, "SiteID": {"type": "integer", "description": "Identifier for the site where equipment is located", "minimum": 1}, "MaintenanceType": {"type": "string", "description": "Type of maintenance performed", "enum": ["PreventiveMaintenance", "CorrectiveMaintenance", "PredictiveMaintenance", "EmergencyMaintenance"]}, "MaintenanceHours": {"type": "number", "description": "Number of hours spent on maintenance", "minimum": 0}, "TransactionDate": {"type": "string", "format": "date", "description": "Date when the maintenance was performed (YYYY-MM-DD format)"}, "PriorityScore": {"type": "number", "description": "Numerical priority score for the maintenance task", "minimum": 0, "maximum": 100}, "PriorityAssessment": {"type": "string", "description": "Qualitative assessment of maintenance priority", "enum": ["Critical", "High", "Medium", "Low"]}, "ImmediateActions": {"type": "array", "description": "List of actions that must be taken immediately", "items": {"type": "string", "minLength": 1}}, "RecommendedActions": {"type": "array", "description": "List of recommended actions for future maintenance", "items": {"type": "string", "minLength": 1}}, "RecommendedTimeline": {"type": "string", "description": "Recommended timeline for completing actions", "enum": ["Immediate", "Within 24 hours", "Within 1 week", "Within 1 month", "Within 3 months", "Within 6 months", "Next scheduled maintenance"]}, "RiskLevel": {"type": "string", "description": "Risk level assessment for equipment or operation", "enum": ["Critical", "High", "Medium", "Low", "Minimal"]}}, "additionalProperties": false}